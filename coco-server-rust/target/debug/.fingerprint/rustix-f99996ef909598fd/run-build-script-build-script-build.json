{"rustc": 6694675083057748860, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10004434995811528692, "build_script_build", false, 10828290752485259067]], "local": [{"RerunIfChanged": {"output": "debug/build/rustix-f99996ef909598fd/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}