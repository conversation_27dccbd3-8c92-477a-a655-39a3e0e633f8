{"rustc": 6694675083057748860, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 5347358027863023418, "path": 18049956469285071898, "deps": [[1573238666360410412, "rand_chacha", false, 7733527636925081681], [4684437522915235464, "libc", false, 14889154772738619546], [18130209639506977569, "rand_core", false, 6897864248137868007]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-1a99557bb909d29c/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}