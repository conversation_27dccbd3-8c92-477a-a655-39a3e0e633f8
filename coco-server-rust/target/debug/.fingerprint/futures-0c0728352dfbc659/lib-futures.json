{"rustc": 6694675083057748860, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 336243669335521001, "path": 11362435317719532699, "deps": [[5103565458935487, "futures_io", false, 17283903714404977332], [1811549171721445101, "futures_channel", false, 17929831132500140881], [7013762810557009322, "futures_sink", false, 8388499781222529088], [7620660491849607393, "futures_core", false, 5451327616162920364], [10629569228670356391, "futures_util", false, 2287442821011104536], [12779779637805422465, "futures_executor", false, 17737406301860763785], [16240732885093539806, "futures_task", false, 7304974315561014614]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-0c0728352dfbc659/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}