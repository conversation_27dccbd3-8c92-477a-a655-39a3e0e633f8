{"rustc": 6694675083057748860, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 8276155916380437441, "path": 3752561470885484785, "deps": [[2828590642173593838, "cfg_if", false, 8561685339905901311]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-56400b2378463944/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}