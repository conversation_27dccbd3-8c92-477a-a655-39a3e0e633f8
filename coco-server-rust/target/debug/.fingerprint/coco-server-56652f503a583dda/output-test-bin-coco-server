{"$message_type":"diagnostic","message":"this import is redundant","code":{"code":"clippy::single_component_path_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/main.rs","byte_start":454,"byte_end":477,"line_start":21,"line_end":21,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"use tracing_subscriber;","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_component_path_imports","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::single_component_path_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove it entirely","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":454,"byte_end":478,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing_subscriber;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this import is redundant\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:21:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing_subscriber;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: remove it entirely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_component_path_imports\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::single_component_path_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `DEFAULT_USER_PASSWORD_KEY` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/account_handler.rs","byte_start":1031,"byte_end":1056,"line_start":44,"line_end":44,"column_start":7,"column_end":32,"is_primary":true,"text":[{"text":"const DEFAULT_USER_PASSWORD_KEY: &str = \"default_user_password\";","highlight_start":7,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `DEFAULT_USER_PASSWORD_KEY` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/account_handler.rs:44:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst DEFAULT_USER_PASSWORD_KEY: &str = \"default_user_password\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `WebSocketError` and `InvalidRequest` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/error/error.rs","byte_start":56,"byte_end":65,"line_start":4,"line_end":4,"column_start":10,"column_end":19,"is_primary":false,"text":[{"text":"pub enum CocoError {","highlight_start":10,"highlight_end":19}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/error/error.rs","byte_start":496,"byte_end":510,"line_start":21,"line_end":21,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    WebSocketError(String),","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/error/error.rs","byte_start":630,"byte_end":644,"line_start":27,"line_end":27,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    InvalidRequest(String),","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`CocoError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variants `WebSocketError` and `InvalidRequest` are never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error/error.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum CocoError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    WebSocketError(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InvalidRequest(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `CocoError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated functions `config`, `websocket`, and `invalid_request` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/error/error.rs","byte_start":752,"byte_end":766,"line_start":33,"line_end":33,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl CocoError {","highlight_start":1,"highlight_end":15}],"label":"associated functions in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/error/error.rs","byte_start":780,"byte_end":786,"line_start":34,"line_end":34,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn config(msg: &str) -> Self {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/error/error.rs","byte_start":972,"byte_end":981,"line_start":42,"line_end":42,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn websocket(msg: &str) -> Self {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/error/error.rs","byte_start":1174,"byte_end":1189,"line_start":50,"line_end":50,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn invalid_request(msg: &str) -> Self {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated functions `config`, `websocket`, and `invalid_request` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error/error.rs:34:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CocoError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated functions in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn config(msg: &str) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn websocket(msg: &str) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn invalid_request(msg: &str) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `AuthManager` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/auth/auth_manager.rs","byte_start":113,"byte_end":124,"line_start":6,"line_end":6,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"pub struct AuthManager {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `AuthManager` is never constructed\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/auth/auth_manager.rs:6:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct AuthManager {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `is_valid_token`, `add_token`, `remove_token`, and `list_tokens` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/auth/auth_manager.rs","byte_start":165,"byte_end":181,"line_start":10,"line_end":10,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl AuthManager {","highlight_start":1,"highlight_end":17}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/auth/auth_manager.rs","byte_start":195,"byte_end":198,"line_start":11,"line_end":11,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(config_manager: &ConfigManager) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/auth/auth_manager.rs","byte_start":1286,"byte_end":1300,"line_start":40,"line_end":40,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn is_valid_token(&self, token: &str) -> bool {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/auth/auth_manager.rs","byte_start":1395,"byte_end":1404,"line_start":44,"line_end":44,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn add_token(&mut self, token: String) {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/auth/auth_manager.rs","byte_start":1496,"byte_end":1508,"line_start":48,"line_end":48,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn remove_token(&mut self, token: &str) {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/auth/auth_manager.rs","byte_start":1598,"byte_end":1609,"line_start":52,"line_end":52,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn list_tokens(&self) -> Vec<String> {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `is_valid_token`, `add_token`, `remove_token`, and `list_tokens` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/auth/auth_manager.rs:11:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl AuthManager {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(config_manager: &ConfigManager) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_valid_token(&self, token: &str) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_token(&mut self, token: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn remove_token(&mut self, token: &str) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn list_tokens(&self) -> Vec<String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `TlsConfig` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/tls/tls_config.rs","byte_start":210,"byte_end":219,"line_start":8,"line_end":8,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"pub struct TlsConfig {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `TlsConfig` is never constructed\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/tls/tls_config.rs:8:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct TlsConfig {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `load_config`, and `is_enabled` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/tls/tls_config.rs","byte_start":278,"byte_end":292,"line_start":13,"line_end":13,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl TlsConfig {","highlight_start":1,"highlight_end":15}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tls/tls_config.rs","byte_start":306,"byte_end":309,"line_start":14,"line_end":14,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(cert_path: String, key_path: String) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tls/tls_config.rs","byte_start":450,"byte_end":461,"line_start":21,"line_end":21,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn load_config(&self) -> Result<ServerConfig> {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tls/tls_config.rs","byte_start":1973,"byte_end":1983,"line_start":59,"line_end":59,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn is_enabled(&self) -> bool {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `load_config`, and `is_enabled` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/tls/tls_config.rs:14:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl TlsConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(cert_path: String, key_path: String) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn load_config(&self) -> Result<ServerConfig> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_enabled(&self) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/config/config_manager.rs","byte_start":808,"byte_end":870,"line_start":24,"line_end":24,"column_start":40,"column_end":102,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\", config_path, e))","highlight_start":40,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::uninlined_format_args)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/config/config_manager.rs","byte_start":846,"byte_end":846,"line_start":24,"line_end":24,"column_start":78,"column_end":78,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\", config_path, e))","highlight_start":78,"highlight_end":78}],"label":null,"suggested_replacement":"config_path","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":851,"byte_end":851,"line_start":24,"line_end":24,"column_start":83,"column_end":83,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\", config_path, e))","highlight_start":83,"highlight_end":83}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":853,"byte_end":866,"line_start":24,"line_end":24,"column_start":85,"column_end":98,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\", config_path, e))","highlight_start":85,"highlight_end":98}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":866,"byte_end":869,"line_start":24,"line_end":24,"column_start":98,"column_end":101,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\", config_path, e))","highlight_start":98,"highlight_end":101}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/config_manager.rs:24:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\", config_path, e))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::uninlined_format_args)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                CocoError::ConfigError(format!(\"Failed to read config file '{}': {}\"\u001b[0m\u001b[0m\u001b[38;5;9m, config_path, e\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                CocoError::ConfigError(format!(\"Failed to read config file '{\u001b[0m\u001b[0m\u001b[38;5;10mconfig_path\u001b[0m\u001b[0m}': {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/config/config_manager.rs","byte_start":1139,"byte_end":1202,"line_start":31,"line_end":31,"column_start":40,"column_end":103,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\", config_path, e))","highlight_start":40,"highlight_end":103}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/config/config_manager.rs","byte_start":1178,"byte_end":1178,"line_start":31,"line_end":31,"column_start":79,"column_end":79,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\", config_path, e))","highlight_start":79,"highlight_end":79}],"label":null,"suggested_replacement":"config_path","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":1183,"byte_end":1183,"line_start":31,"line_end":31,"column_start":84,"column_end":84,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\", config_path, e))","highlight_start":84,"highlight_end":84}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":1185,"byte_end":1198,"line_start":31,"line_end":31,"column_start":86,"column_end":99,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\", config_path, e))","highlight_start":86,"highlight_end":99}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":1198,"byte_end":1201,"line_start":31,"line_end":31,"column_start":99,"column_end":102,"is_primary":true,"text":[{"text":"                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\", config_path, e))","highlight_start":99,"highlight_end":102}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/config_manager.rs:31:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\", config_path, e))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                CocoError::ConfigError(format!(\"Failed to parse config file '{}': {}\"\u001b[0m\u001b[0m\u001b[38;5;9m, config_path, e\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                CocoError::ConfigError(format!(\"Failed to parse config file '{\u001b[0m\u001b[0m\u001b[38;5;10mconfig_path\u001b[0m\u001b[0m}': {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/config/config_manager.rs","byte_start":7390,"byte_end":7502,"line_start":192,"line_end":192,"column_start":25,"column_end":137,"is_primary":true,"text":[{"text":"                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\", web_port, api_port)","highlight_start":25,"highlight_end":137}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/config/config_manager.rs","byte_start":7418,"byte_end":7418,"line_start":192,"line_end":192,"column_start":53,"column_end":53,"is_primary":true,"text":[{"text":"                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\", web_port, api_port)","highlight_start":53,"highlight_end":53}],"label":null,"suggested_replacement":"web_port","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":7444,"byte_end":7444,"line_start":192,"line_end":192,"column_start":79,"column_end":79,"is_primary":true,"text":[{"text":"                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\", web_port, api_port)","highlight_start":79,"highlight_end":79}],"label":null,"suggested_replacement":"api_port","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":7481,"byte_end":7491,"line_start":192,"line_end":192,"column_start":116,"column_end":126,"is_primary":true,"text":[{"text":"                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\", web_port, api_port)","highlight_start":116,"highlight_end":126}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":7491,"byte_end":7501,"line_start":192,"line_end":192,"column_start":126,"column_end":136,"is_primary":true,"text":[{"text":"                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\", web_port, api_port)","highlight_start":126,"highlight_end":136}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/config_manager.rs:192:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\", web_port, api_port)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                        format!(\"Web service port ({}) and API service port ({}) conflict. They must be different.\"\u001b[0m\u001b[0m\u001b[38;5;9m, web_port, api_port\u001b[0m\u001b[0m)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                        format!(\"Web service port ({\u001b[0m\u001b[0m\u001b[38;5;10mweb_port\u001b[0m\u001b[0m}) and API service port ({\u001b[0m\u001b[0m\u001b[38;5;10mapi_port\u001b[0m\u001b[0m}) conflict. They must be different.\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this comparison involving the minimum or maximum element for this type contains a case that is always true or always false","code":{"code":"clippy::absurd_extreme_comparisons","explanation":null},"level":"error","spans":[{"file_name":"src/config/config_manager.rs","byte_start":7843,"byte_end":7856,"line_start":205,"line_end":205,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"        if port == 0 || port >= 65535 {","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"because `65535` is the maximum value for this type, the case where the two sides are not equal never occurs, consider using `port == 65535` instead","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#absurd_extreme_comparisons","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[deny(clippy::absurd_extreme_comparisons)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: this comparison involving the minimum or maximum element for this type contains a case that is always true or always false\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/config_manager.rs:205:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if port == 0 || port >= 65535 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: because `65535` is the maximum value for this type, the case where the two sides are not equal never occurs, consider using `port == 65535` instead\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#absurd_extreme_comparisons\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[deny(clippy::absurd_extreme_comparisons)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/config/config_manager.rs","byte_start":7905,"byte_end":7969,"line_start":206,"line_end":206,"column_start":47,"column_end":111,"is_primary":true,"text":[{"text":"            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\", service_name, port)));","highlight_start":47,"highlight_end":111}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/config/config_manager.rs","byte_start":7923,"byte_end":7923,"line_start":206,"line_end":206,"column_start":65,"column_end":65,"is_primary":true,"text":[{"text":"            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\", service_name, port)));","highlight_start":65,"highlight_end":65}],"label":null,"suggested_replacement":"service_name","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":7946,"byte_end":7946,"line_start":206,"line_end":206,"column_start":88,"column_end":88,"is_primary":true,"text":[{"text":"            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\", service_name, port)));","highlight_start":88,"highlight_end":88}],"label":null,"suggested_replacement":"port","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":7948,"byte_end":7962,"line_start":206,"line_end":206,"column_start":90,"column_end":104,"is_primary":true,"text":[{"text":"            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\", service_name, port)));","highlight_start":90,"highlight_end":104}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/config/config_manager.rs","byte_start":7962,"byte_end":7968,"line_start":206,"line_end":206,"column_start":104,"column_end":110,"is_primary":true,"text":[{"text":"            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\", service_name, port)));","highlight_start":104,"highlight_end":110}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/config_manager.rs:206:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\", service_name, port)));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return Err(CocoError::ConfigError(format!(\"Invalid {} port configuration: {}\"\u001b[0m\u001b[0m\u001b[38;5;9m, service_name, port\u001b[0m\u001b[0m)));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return Err(CocoError::ConfigError(format!(\"Invalid {\u001b[0m\u001b[0m\u001b[38;5;10mservice_name\u001b[0m\u001b[0m} port configuration: {\u001b[0m\u001b[0m\u001b[38;5;10mport\u001b[0m\u001b[0m}\")));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/account_handler.rs","byte_start":5062,"byte_end":5108,"line_start":145,"line_end":145,"column_start":41,"column_end":87,"is_primary":true,"text":[{"text":"        .map_err(|e| CocoError::server(&format!(\"Failed to generate JWT token: {}\", e)))?;","highlight_start":41,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/handlers/account_handler.rs","byte_start":5102,"byte_end":5102,"line_start":145,"line_end":145,"column_start":81,"column_end":81,"is_primary":true,"text":[{"text":"        .map_err(|e| CocoError::server(&format!(\"Failed to generate JWT token: {}\", e)))?;","highlight_start":81,"highlight_end":81}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/account_handler.rs","byte_start":5104,"byte_end":5107,"line_start":145,"line_end":145,"column_start":83,"column_end":86,"is_primary":true,"text":[{"text":"        .map_err(|e| CocoError::server(&format!(\"Failed to generate JWT token: {}\", e)))?;","highlight_start":83,"highlight_end":86}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/account_handler.rs:145:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| CocoError::server(&format!(\"Failed to generate JWT token: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| CocoError::server(&format!(\"Failed to generate JWT token: {}\"\u001b[0m\u001b[0m\u001b[38;5;9m, e\u001b[0m\u001b[0m)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| CocoError::server(&format!(\"Failed to generate JWT token: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/info_handler.rs","byte_start":2609,"byte_end":2658,"line_start":98,"line_end":98,"column_start":45,"column_end":94,"is_primary":true,"text":[{"text":"        (StatusCode::INTERNAL_SERVER_ERROR, format!(\"Failed to create health checker: {}\", e))","highlight_start":45,"highlight_end":94}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/handlers/info_handler.rs","byte_start":2652,"byte_end":2652,"line_start":98,"line_end":98,"column_start":88,"column_end":88,"is_primary":true,"text":[{"text":"        (StatusCode::INTERNAL_SERVER_ERROR, format!(\"Failed to create health checker: {}\", e))","highlight_start":88,"highlight_end":88}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/info_handler.rs","byte_start":2654,"byte_end":2657,"line_start":98,"line_end":98,"column_start":90,"column_end":93,"is_primary":true,"text":[{"text":"        (StatusCode::INTERNAL_SERVER_ERROR, format!(\"Failed to create health checker: {}\", e))","highlight_start":90,"highlight_end":93}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/info_handler.rs:98:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (StatusCode::INTERNAL_SERVER_ERROR, format!(\"Failed to create health checker: {}\", e))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        (StatusCode::INTERNAL_SERVER_ERROR, format!(\"Failed to create health checker: {}\"\u001b[0m\u001b[0m\u001b[38;5;9m, e\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        (StatusCode::INTERNAL_SERVER_ERROR, format!(\"Failed to create health checker: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/info_handler.rs","byte_start":3857,"byte_end":3893,"line_start":138,"line_end":138,"column_start":20,"column_end":56,"is_primary":true,"text":[{"text":"    let endpoint = format!(\"http://localhost:{}\", port);","highlight_start":20,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/handlers/info_handler.rs","byte_start":3884,"byte_end":3884,"line_start":138,"line_end":138,"column_start":47,"column_end":47,"is_primary":true,"text":[{"text":"    let endpoint = format!(\"http://localhost:{}\", port);","highlight_start":47,"highlight_end":47}],"label":null,"suggested_replacement":"port","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/info_handler.rs","byte_start":3886,"byte_end":3892,"line_start":138,"line_end":138,"column_start":49,"column_end":55,"is_primary":true,"text":[{"text":"    let endpoint = format!(\"http://localhost:{}\", port);","highlight_start":49,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/info_handler.rs:138:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let endpoint = format!(\"http://localhost:{}\", port);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let endpoint = format!(\"http://localhost:{}\"\u001b[0m\u001b[0m\u001b[38;5;9m, port\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let endpoint = format!(\"http://localhost:{\u001b[0m\u001b[0m\u001b[38;5;10mport\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/websocket_handler.rs","byte_start":1795,"byte_end":1820,"line_start":67,"line_end":67,"column_start":28,"column_end":53,"is_primary":true,"text":[{"text":"            let response = format!(\"Echo: {}\", text);","highlight_start":28,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/handlers/websocket_handler.rs","byte_start":1811,"byte_end":1811,"line_start":67,"line_end":67,"column_start":44,"column_end":44,"is_primary":true,"text":[{"text":"            let response = format!(\"Echo: {}\", text);","highlight_start":44,"highlight_end":44}],"label":null,"suggested_replacement":"text","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/websocket_handler.rs","byte_start":1813,"byte_end":1819,"line_start":67,"line_end":67,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"            let response = format!(\"Echo: {}\", text);","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/websocket_handler.rs:67:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let response = format!(\"Echo: {}\", text);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            let response = format!(\"Echo: {}\"\u001b[0m\u001b[0m\u001b[38;5;9m, text\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            let response = format!(\"Echo: {\u001b[0m\u001b[0m\u001b[38;5;10mtext\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"stripping a prefix manually","code":{"code":"clippy::manual_strip","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":1203,"byte_end":1220,"line_start":38,"line_end":38,"column_start":30,"column_end":47,"is_primary":true,"text":[{"text":"                        Some(&auth_header[7..]) // 移除\"Bearer \"前缀","highlight_start":30,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the prefix was tested here","code":null,"level":"note","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":1134,"byte_end":1172,"line_start":37,"line_end":37,"column_start":21,"column_end":59,"is_primary":true,"text":[{"text":"                    if auth_header.starts_with(\"Bearer \") {","highlight_start":21,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_strip","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::manual_strip)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try using the `strip_prefix` method","code":null,"level":"help","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":1134,"byte_end":1172,"line_start":37,"line_end":37,"column_start":21,"column_end":59,"is_primary":true,"text":[{"text":"                    if auth_header.starts_with(\"Bearer \") {","highlight_start":21,"highlight_end":59}],"label":null,"suggested_replacement":"if let Some(<stripped>) = auth_header.strip_prefix(\"Bearer \") ","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/middleware/auth_middleware.rs","byte_start":1203,"byte_end":1220,"line_start":38,"line_end":38,"column_start":30,"column_end":47,"is_primary":true,"text":[{"text":"                        Some(&auth_header[7..]) // 移除\"Bearer \"前缀","highlight_start":30,"highlight_end":47}],"label":null,"suggested_replacement":"<stripped>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: stripping a prefix manually\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth_middleware.rs:38:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        Some(&auth_header[7..]) // 移除\"Bearer \"前缀\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the prefix was tested here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth_middleware.rs:37:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    if auth_header.starts_with(\"Bearer \") {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_strip\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::manual_strip)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try using the `strip_prefix` method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10mif let Some(<stripped>) = auth_header.strip_prefix(\"Bearer \") \u001b[0m\u001b[0m{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                        Some(\u001b[0m\u001b[0m\u001b[38;5;10m<stripped>\u001b[0m\u001b[0m) // 移除\"Bearer \"前缀\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unneeded `return` statement","code":{"code":"clippy::needless_return","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":3648,"byte_end":3663,"line_start":97,"line_end":97,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"        Ok(_) => return Ok(true),","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_return","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::needless_return)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove `return`","code":null,"level":"help","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":3648,"byte_end":3663,"line_start":97,"line_end":97,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"        Ok(_) => return Ok(true),","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":"Ok(true)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unneeded `return` statement\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth_middleware.rs:97:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Ok(_) => return Ok(true),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_return\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::needless_return)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove `return`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        Ok(_) => \u001b[0m\u001b[0m\u001b[38;5;9mreturn Ok(true)\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        Ok(_) => \u001b[0m\u001b[0m\u001b[38;5;10mOk(true)\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"length comparison to zero","code":{"code":"clippy::len_zero","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":4086,"byte_end":4101,"line_start":110,"line_end":110,"column_start":20,"column_end":35,"is_primary":true,"text":[{"text":"                if token.len() > 0 {","highlight_start":20,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::len_zero)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"using `!is_empty` is clearer and more explicit","code":null,"level":"help","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":4086,"byte_end":4101,"line_start":110,"line_end":110,"column_start":20,"column_end":35,"is_primary":true,"text":[{"text":"                if token.len() > 0 {","highlight_start":20,"highlight_end":35}],"label":null,"suggested_replacement":"!token.is_empty()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: length comparison to zero\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth_middleware.rs:110:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if token.len() > 0 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: using `!is_empty` is clearer and more explicit: `!token.is_empty()`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::len_zero)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":4579,"byte_end":4613,"line_start":127,"line_end":127,"column_start":9,"column_end":43,"is_primary":true,"text":[{"text":"        format!(\"{}***{}\", prefix, suffix)","highlight_start":9,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/middleware/auth_middleware.rs","byte_start":4589,"byte_end":4589,"line_start":127,"line_end":127,"column_start":19,"column_end":19,"is_primary":true,"text":[{"text":"        format!(\"{}***{}\", prefix, suffix)","highlight_start":19,"highlight_end":19}],"label":null,"suggested_replacement":"prefix","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/middleware/auth_middleware.rs","byte_start":4594,"byte_end":4594,"line_start":127,"line_end":127,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"        format!(\"{}***{}\", prefix, suffix)","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":"suffix","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/middleware/auth_middleware.rs","byte_start":4596,"byte_end":4604,"line_start":127,"line_end":127,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"        format!(\"{}***{}\", prefix, suffix)","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/middleware/auth_middleware.rs","byte_start":4604,"byte_end":4612,"line_start":127,"line_end":127,"column_start":34,"column_end":42,"is_primary":true,"text":[{"text":"        format!(\"{}***{}\", prefix, suffix)","highlight_start":34,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth_middleware.rs:127:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        format!(\"{}***{}\", prefix, suffix)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        format!(\"{}***{}\"\u001b[0m\u001b[0m\u001b[38;5;9m, prefix, suffix\u001b[0m\u001b[0m)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mprefix\u001b[0m\u001b[0m}***{\u001b[0m\u001b[0m\u001b[38;5;10msuffix\u001b[0m\u001b[0m}\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"module has the same name as its containing module","code":{"code":"clippy::module_inception","explanation":null},"level":"warning","spans":[{"file_name":"src/error/mod.rs","byte_start":0,"byte_end":14,"line_start":1,"line_end":1,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod error;","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#module_inception","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::module_inception)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: module has the same name as its containing module\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error/mod.rs:1:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod error;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#module_inception\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::module_inception)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":2777,"byte_end":2815,"line_start":93,"line_end":93,"column_start":18,"column_end":56,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/health\", es_url))","highlight_start":18,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::needless_borrows_for_generic_args)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":2777,"byte_end":2815,"line_start":93,"line_end":93,"column_start":18,"column_end":56,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/health\", es_url))","highlight_start":18,"highlight_end":56}],"label":null,"suggested_replacement":"format!(\"{}/_cluster/health\", es_url)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:93:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get(&format!(\"{}/_cluster/health\", es_url))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `format!(\"{}/_cluster/health\", es_url)`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::needless_borrows_for_generic_args)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":2778,"byte_end":2815,"line_start":93,"line_end":93,"column_start":19,"column_end":56,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/health\", es_url))","highlight_start":19,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":2788,"byte_end":2788,"line_start":93,"line_end":93,"column_start":29,"column_end":29,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/health\", es_url))","highlight_start":29,"highlight_end":29}],"label":null,"suggested_replacement":"es_url","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/health/health_checker.rs","byte_start":2806,"byte_end":2814,"line_start":93,"line_end":93,"column_start":47,"column_end":55,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/health\", es_url))","highlight_start":47,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:93:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get(&format!(\"{}/_cluster/health\", es_url))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            .get(&format!(\"{}/_cluster/health\"\u001b[0m\u001b[0m\u001b[38;5;9m, es_url\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            .get(&format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mes_url\u001b[0m\u001b[0m}/_cluster/health\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5448,"byte_end":5483,"line_start":167,"line_end":167,"column_start":18,"column_end":53,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cat/indices\", es_url))","highlight_start":18,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5448,"byte_end":5483,"line_start":167,"line_end":167,"column_start":18,"column_end":53,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cat/indices\", es_url))","highlight_start":18,"highlight_end":53}],"label":null,"suggested_replacement":"format!(\"{}/_cat/indices\", es_url)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:167:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get(&format!(\"{}/_cat/indices\", es_url))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `format!(\"{}/_cat/indices\", es_url)`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5449,"byte_end":5483,"line_start":167,"line_end":167,"column_start":19,"column_end":53,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cat/indices\", es_url))","highlight_start":19,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5459,"byte_end":5459,"line_start":167,"line_end":167,"column_start":29,"column_end":29,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cat/indices\", es_url))","highlight_start":29,"highlight_end":29}],"label":null,"suggested_replacement":"es_url","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/health/health_checker.rs","byte_start":5474,"byte_end":5482,"line_start":167,"line_end":167,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cat/indices\", es_url))","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:167:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get(&format!(\"{}/_cat/indices\", es_url))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            .get(&format!(\"{}/_cat/indices\"\u001b[0m\u001b[0m\u001b[38;5;9m, es_url\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            .get(&format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mes_url\u001b[0m\u001b[0m}/_cat/indices\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5771,"byte_end":5813,"line_start":176,"line_end":176,"column_start":30,"column_end":72,"is_primary":true,"text":[{"text":"                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))","highlight_start":30,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5771,"byte_end":5813,"line_start":176,"line_end":176,"column_start":30,"column_end":72,"is_primary":true,"text":[{"text":"                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))","highlight_start":30,"highlight_end":72}],"label":null,"suggested_replacement":"format!(\"{}/_cat/indices/coco_*\", es_url)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:176:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `format!(\"{}/_cat/indices/coco_*\", es_url)`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5772,"byte_end":5813,"line_start":176,"line_end":176,"column_start":31,"column_end":72,"is_primary":true,"text":[{"text":"                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))","highlight_start":31,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":5782,"byte_end":5782,"line_start":176,"line_end":176,"column_start":41,"column_end":41,"is_primary":true,"text":[{"text":"                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))","highlight_start":41,"highlight_end":41}],"label":null,"suggested_replacement":"es_url","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/health/health_checker.rs","byte_start":5804,"byte_end":5812,"line_start":176,"line_end":176,"column_start":63,"column_end":71,"is_primary":true,"text":[{"text":"                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))","highlight_start":63,"highlight_end":71}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:176:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .get(&format!(\"{}/_cat/indices/coco_*\", es_url))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                        .get(&format!(\"{}/_cat/indices/coco_*\"\u001b[0m\u001b[0m\u001b[38;5;9m, es_url\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                        .get(&format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mes_url\u001b[0m\u001b[0m}/_cat/indices/coco_*\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":7742,"byte_end":7779,"line_start":227,"line_end":227,"column_start":18,"column_end":55,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/stats\", es_url))","highlight_start":18,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":7742,"byte_end":7779,"line_start":227,"line_end":227,"column_start":18,"column_end":55,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/stats\", es_url))","highlight_start":18,"highlight_end":55}],"label":null,"suggested_replacement":"format!(\"{}/_cluster/stats\", es_url)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:227:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get(&format!(\"{}/_cluster/stats\", es_url))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `format!(\"{}/_cluster/stats\", es_url)`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/health/health_checker.rs","byte_start":7743,"byte_end":7779,"line_start":227,"line_end":227,"column_start":19,"column_end":55,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/stats\", es_url))","highlight_start":19,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/health/health_checker.rs","byte_start":7753,"byte_end":7753,"line_start":227,"line_end":227,"column_start":29,"column_end":29,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/stats\", es_url))","highlight_start":29,"highlight_end":29}],"label":null,"suggested_replacement":"es_url","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/health/health_checker.rs","byte_start":7770,"byte_end":7778,"line_start":227,"line_end":227,"column_start":46,"column_end":54,"is_primary":true,"text":[{"text":"            .get(&format!(\"{}/_cluster/stats\", es_url))","highlight_start":46,"highlight_end":54}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/health/health_checker.rs:227:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get(&format!(\"{}/_cluster/stats\", es_url))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            .get(&format!(\"{}/_cluster/stats\"\u001b[0m\u001b[0m\u001b[38;5;9m, es_url\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            .get(&format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mes_url\u001b[0m\u001b[0m}/_cluster/stats\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 29 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error; 29 warnings emitted\u001b[0m\n\n"}
