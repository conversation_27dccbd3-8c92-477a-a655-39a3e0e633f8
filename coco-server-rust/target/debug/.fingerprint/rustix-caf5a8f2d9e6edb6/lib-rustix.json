{"rustc": 6694675083057748860, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 12102173190008962477, "deps": [[4684437522915235464, "libc", false, 14889154772738619546], [7896293946984509699, "bitflags", false, 2018595465915069626], [8253628577145923712, "libc_errno", false, 5293195537994694840], [10004434995811528692, "build_script_build", false, 4256448716705998795]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-caf5a8f2d9e6edb6/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}