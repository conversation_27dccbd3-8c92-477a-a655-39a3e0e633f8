{"rustc": 6694675083057748860, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 8276155916380437441, "path": 13831261828444524738, "deps": [[5157631553186200874, "num_traits", false, 13102201138819406246], [7910860254152155345, "iana_time_zone", false, 10086203701879451445], [9689903380558560274, "serde", false, 7982615963999043816]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-eef798af1104cd12/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}