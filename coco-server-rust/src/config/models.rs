use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Default)]
pub struct Config {
    pub env: Option<EnvConfig>,
    pub coco: Option<CocoConfig>,
    // 其他配置项可以根据需要添加
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct EnvConfig {
    #[serde(rename = "ES_ENDPOINT")]
    pub es_endpoint: Option<String>,
    #[serde(rename = "ES_USERNAME")]
    pub es_username: Option<String>,
    #[serde(rename = "ES_PASSWORD")]
    pub es_password: Option<String>,
    #[serde(rename = "WEB_BINDING")]
    pub web_binding: Option<String>,
    #[serde(rename = "API_BINDING")]
    pub api_binding: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct CocoConfig {
    pub server: Option<CocoServerConfig>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct CocoServerConfig {
    pub public: Option<bool>,
    pub name: Option<String>,
    #[serde(rename = "encode_icon_to_base64")]
    pub encode_icon_to_base64: Option<bool>,
    #[serde(rename = "minimal_client_version")]
    pub minimal_client_version: Option<VersionConfig>,
    pub provider: Option<ProviderConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct VersionConfig {
    pub number: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ProviderConfig {
    pub name: Option<String>,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub website: Option<String>,
    #[serde(rename = "eula")]
    pub eula: Option<String>,
    #[serde(rename = "privacy_policy")]
    pub privacy_policy: Option<String>,
    pub banner: Option<String>,
    #[serde(rename = "auth_provider")]
    pub auth_provider: Option<AuthProviderConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AuthProviderConfig {
    pub sso: Option<SsoConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SsoConfig {
    pub url: Option<String>,
}