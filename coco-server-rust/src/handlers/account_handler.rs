use axum::{
    extract::State,
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{info, error, debug, warn};
use bcrypt::{hash, verify, DEFAULT_COST};
use jsonwebtoken::{encode, Header, EncodingKey};

use crate::config::config_manager::ConfigManager;
use crate::error::result::{AxumResult};
use crate::error::error::CocoError;

// 登录请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginRequest {
    pub password: String,
}

// 登录响应模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub username: String,
    pub id: String,
    pub expire_in: i64,
    pub status: String,
}

// 用户信息结构
#[derive(Debug, <PERSON><PERSON>)]
pub struct UserInfo {
    pub user_id: String,
    pub username: String,
    pub email: String,
    pub roles: Vec<String>,
}

// JWT Claims结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserClaims {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub exp: usize,
}

// 默认用户常量
const DEFAULT_USER_LOGIN: &str = "coco-default-user";
const DEFAULT_USER_PASSWORD_KEY: &str = "default_user_password";

pub async fn login_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(payload): Json<LoginRequest>,
) -> AxumResult<impl IntoResponse> {
    info!("Handling /account/login request");
    
    // 验证密码
    match verify_password(&config_manager, &payload.password).await {
        Ok(is_valid) => {
            if is_valid {
                // 获取用户信息
                match get_user_info().await {
                    Ok(user_info) => {
                        // 生成JWT访问令牌
                        match generate_jwt_token(&user_info).await {
                            Ok(token_data) => {
                                let response = LoginResponse {
                                    access_token: token_data.access_token,
                                    username: user_info.username,
                                    id: user_info.user_id,
                                    expire_in: token_data.expire_in,
                                    status: "ok".to_string(),
                                };
                                Ok((StatusCode::OK, Json(response)))
                            }
                            Err(e) => {
                                error!("Failed to generate JWT token: {}", e);
                                Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to generate access token".to_string()))
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to get user info: {}", e);
                        Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to get user info".to_string()))
                    }
                }
            } else {
                info!("Login failed: invalid password");
                Err((StatusCode::FORBIDDEN, "failed to login".to_string()))
            }
        }
        Err(e) => {
            error!("Password verification failed: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Password verification failed".to_string()))
        }
    }
}

async fn verify_password(config_manager: &ConfigManager, password: &str) -> Result<bool, CocoError> {
    // 获取保存的密码哈希
    let saved_hash = get_saved_password_hash(config_manager).await?;
    
    // 验证密码
    match verify(password, &saved_hash) {
        Ok(is_valid) => Ok(is_valid),
        Err(e) => {
            error!("Password verification error: {}", e);
            Err(CocoError::auth("Password verification failed"))
        }
    }
}

async fn get_saved_password_hash(_config_manager: &ConfigManager) -> Result<String, CocoError> {
    use std::fs;
    
    // 首先检查环境变量中的初始密码（向后兼容）
    if let Ok(initial_password) = std::env::var("EASYSEARCH_INITIAL_ADMIN_PASSWORD") {
        if !initial_password.is_empty() {
            debug!("Using initial admin password from environment variable");
            // 对初始密码进行哈希处理并返回
            return hash_password(&initial_password).await;
        }
    }
    
    // 尝试从.password文件中读取保存的密码哈希
    match fs::read_to_string(".password") {
        Ok(hashed_password) => {
            debug!("Using password hash from .password file");
            Ok(hashed_password.trim().to_string())
        }
        Err(e) => {
            error!("Failed to read .password file: {}", e);
            // 如果文件不存在，使用默认密码（仅用于开发环境）
            warn!("No password file found, using default password for development");
            let default_password = "coco123"; // 开发环境默认密码
            hash_password(default_password).await
        }
    }
}

async fn hash_password(password: &str) -> Result<String, CocoError> {
    match hash(password, DEFAULT_COST) {
        Ok(hashed) => Ok(hashed),
        Err(e) => {
            error!("Password hashing error: {}", e);
            Err(CocoError::server("Failed to hash password"))
        }
    }
}

async fn get_user_info() -> Result<UserInfo, CocoError> {
    use std::fs;
    use serde_json::Value;
    
    // 尝试从.user_profile文件中读取用户信息
    match fs::read_to_string(".user_profile") {
        Ok(profile_content) => {
            debug!("Using user profile from .user_profile file");
            match serde_json::from_str::<Value>(&profile_content) {
                Ok(profile_json) => {
                    let user_info = UserInfo {
                        user_id: profile_json.get("email")
                            .and_then(|v| v.as_str())
                            .unwrap_or(DEFAULT_USER_LOGIN)
                            .to_string(),
                        username: profile_json.get("name")
                            .and_then(|v| v.as_str())
                            .unwrap_or(DEFAULT_USER_LOGIN)
                            .to_string(),
                        email: profile_json.get("email")
                            .and_then(|v| v.as_str())
                            .unwrap_or("")
                            .to_string(),
                        roles: vec!["admin".to_string()],
                    };
                    Ok(user_info)
                }
                Err(e) => {
                    error!("Failed to parse user profile JSON: {}", e);
                    // 使用默认用户信息
                    Ok(get_default_user_info())
                }
            }
        }
        Err(e) => {
            debug!("No user profile file found: {}", e);
            // 使用默认用户信息
            Ok(get_default_user_info())
        }
    }
}

fn get_default_user_info() -> UserInfo {
    UserInfo {
        user_id: DEFAULT_USER_LOGIN.to_string(),
        username: DEFAULT_USER_LOGIN.to_string(),
        email: "".to_string(),
        roles: vec!["admin".to_string()],
    }
}

async fn generate_jwt_token(user_info: &UserInfo) -> Result<LoginResponse, CocoError> {
    use std::time::{SystemTime, UNIX_EPOCH};
    
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards")
        .as_secs() as usize;
    
    let expire_in = now as i64 + 86400; // 24小时
    
    let claims = UserClaims {
        user_id: user_info.user_id.clone(),
        username: user_info.username.clone(),
        roles: user_info.roles.clone(),
        exp: now + 86400, // 24小时过期
    };
    
    // 使用一个固定的密钥（在实际应用中应该从安全的地方获取）
    let secret = "coco-server-secret-key";
    let token = encode(&Header::default(), &claims, &EncodingKey::from_secret(secret.as_ref()))
        .map_err(|e| CocoError::server(&format!("Failed to generate JWT token: {}", e)))?;
    
    Ok(LoginResponse {
        access_token: token,
        username: user_info.username.clone(),
        id: user_info.user_id.clone(),
        expire_in,
        status: "ok".to_string(),
    })
}