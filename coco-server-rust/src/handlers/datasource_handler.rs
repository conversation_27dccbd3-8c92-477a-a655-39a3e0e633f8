use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use crate::config::config_manager::ConfigManager;

#[derive(Debug, Serialize, Deserialize)]
pub struct DataSource {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub category: Option<String>,
    pub config: Option<Value>,
    pub created: Option<String>,
    pub updated: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchResponse {
    pub took: u64,
    pub timed_out: bool,
    pub hits: SearchHits,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHits {
    pub total: SearchTotal,
    pub max_score: Option<f64>,
    pub hits: Vec<SearchHit>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchTotal {
    pub value: u64,
    pub relation: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHit {
    #[serde(rename = "_index")]
    pub index: String,
    #[serde(rename = "_type")]
    pub doc_type: String,
    #[serde(rename = "_id")]
    pub id: String,
    #[serde(rename = "_score")]
    pub score: Option<f64>,
    #[serde(rename = "_source")]
    pub source: DataSource,
}

/// 处理 GET /datasource/_search 请求
pub async fn search_datasource_get(
    Query(params): Query<HashMap<String, String>>,
    State(config_manager): State<Arc<ConfigManager>>,
) -> Result<Json<SearchResponse>, StatusCode> {
    tracing::info!("Handling GET /datasource/_search request with params: {:?}", params);
    
    // 模拟数据源搜索结果
    let mock_datasources = vec![
        DataSource {
            id: "ds_001".to_string(),
            name: "Sample DataSource 1".to_string(),
            description: Some("A sample data source for testing".to_string()),
            category: Some("database".to_string()),
            config: Some(json!({"host": "localhost", "port": 5432})),
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
        DataSource {
            id: "ds_002".to_string(),
            name: "Sample DataSource 2".to_string(),
            description: Some("Another sample data source".to_string()),
            category: Some("file".to_string()),
            config: Some(json!({"path": "/data/files"})),
            created: Some("2024-01-02T00:00:00Z".to_string()),
            updated: Some("2024-01-02T00:00:00Z".to_string()),
        },
    ];

    let hits: Vec<SearchHit> = mock_datasources
        .into_iter()
        .enumerate()
        .map(|(i, ds)| SearchHit {
            index: "datasource".to_string(),
            doc_type: "_doc".to_string(),
            id: ds.id.clone(),
            score: Some(1.0 - (i as f64 * 0.1)),
            source: ds,
        })
        .collect();

    let response = SearchResponse {
        took: 5,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 POST /datasource/_search 请求
pub async fn search_datasource_post(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(query): Json<Value>,
) -> Result<Json<SearchResponse>, StatusCode> {
    tracing::info!("Handling POST /datasource/_search request with query: {:?}", query);
    
    // 对于 POST 请求，我们可以处理更复杂的查询
    // 这里先返回相同的模拟数据
    let mock_datasources = vec![
        DataSource {
            id: "ds_001".to_string(),
            name: "Sample DataSource 1".to_string(),
            description: Some("A sample data source for testing".to_string()),
            category: Some("database".to_string()),
            config: Some(json!({"host": "localhost", "port": 5432})),
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
    ];

    let hits: Vec<SearchHit> = mock_datasources
        .into_iter()
        .enumerate()
        .map(|(i, ds)| SearchHit {
            index: "datasource".to_string(),
            doc_type: "_doc".to_string(),
            id: ds.id.clone(),
            score: Some(1.0 - (i as f64 * 0.1)),
            source: ds,
        })
        .collect();

    let response = SearchResponse {
        took: 3,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 OPTIONS /datasource/_search 请求（CORS 预检）
pub async fn search_datasource_options() -> Result<StatusCode, StatusCode> {
    tracing::info!("Handling OPTIONS /datasource/_search request");
    Ok(StatusCode::OK)
}
